# api/prompts/prompt_engine.py
"""
八字分析提示词工程
支持前端参数排列组合的动态提示词生成
"""

import json
import logging
from typing import Dict, Optional, Tuple
from .categories import AnalysisDirection, Domain, TimeScope

logger = logging.getLogger(__name__)


class BaziPromptEngine:
    """八字分析提示词工程"""
    
    def __init__(self):
        self.base_system_prompt = self._get_base_system_prompt()
        self.analysis_templates = self._load_analysis_templates()
        self.domain_templates = self._load_domain_templates()
        self.time_templates = self._load_time_templates()
    
    def generate_prompt(self, analysis_direction: str, domain: str, 
                       time_scope: str, user_question: str = None) -> Tuple[str, str]:
        """
        根据前端参数生成提示词
        
        Args:
            analysis_direction: 分析方向
            domain: 分析领域
            time_scope: 时间范围
            user_question: 用户问题（仅决策支持类型需要）
            
        Returns:
            Tuple[str, str]: (系统提示词, 用户提示词)
        """
        try:
            # 验证参数
            if not self._validate_parameters(analysis_direction, domain, time_scope):
                raise ValueError("参数验证失败")
            
            # 生成系统提示词
            system_prompt = self._build_system_prompt(analysis_direction, domain, time_scope)
            
            # 生成用户提示词
            user_prompt = self._build_user_prompt(analysis_direction, domain, time_scope, user_question)
            
            logger.info(f"生成提示词成功: {analysis_direction}_{domain}_{time_scope}")
            return system_prompt, user_prompt
            
        except Exception as e:
            logger.error(f"生成提示词失败: {e}")
            # 返回默认提示词
            return self._get_default_prompts()
    
    def _validate_parameters(self, analysis_direction: str, domain: str, time_scope: str) -> bool:
        """验证参数有效性"""
        try:
            AnalysisDirection(analysis_direction)
            Domain(domain)
            TimeScope(time_scope)
            return True
        except ValueError:
            return False
    
    def _build_system_prompt(self, analysis_direction: str, domain: str, time_scope: str) -> str:
        """构建系统提示词"""
        # 基础系统提示
        prompt_parts = [self.base_system_prompt]
        
        # 添加分析方向特定的指导
        analysis_guidance = self.analysis_templates.get(analysis_direction, "")
        if analysis_guidance:
            prompt_parts.append(f"\n【分析方向】\n{analysis_guidance}")
        
        # 添加领域特定的专业知识
        domain_knowledge = self.domain_templates.get(domain, "")
        if domain_knowledge:
            prompt_parts.append(f"\n【专业领域】\n{domain_knowledge}")
        
        # 添加时间范围的分析重点
        time_focus = self.time_templates.get(time_scope, "")
        if time_focus:
            prompt_parts.append(f"\n【时间维度】\n{time_focus}")
        
        return "\n".join(prompt_parts)
    
    def _build_user_prompt(self, analysis_direction: str, domain: str, 
                          time_scope: str, user_question: str = None) -> str:
        """构建用户提示词模板"""
        prompt_parts = []
        
        # 基础八字信息模板
        prompt_parts.append("【八字分析请求】")
        prompt_parts.append("八字：{eight_char}")
        prompt_parts.append("性别：{gender}")
        prompt_parts.append("出生日期：{birth_date}")
        prompt_parts.append("")
        
        # 根据分析方向添加特定要求
        if analysis_direction == "basic_analysis":
            prompt_parts.append("请进行基础八字分析，包括：")
            prompt_parts.append("1. 五行强弱分析")
            prompt_parts.append("2. 十神特征解读")
            prompt_parts.append("3. 基本性格特点")
        elif analysis_direction == "contradiction_analysis":
            prompt_parts.append("请分析八字中的矛盾冲突，包括：")
            prompt_parts.append("1. 五行相克关系")
            prompt_parts.append("2. 十神冲突分析")
            prompt_parts.append("3. 化解建议")
        elif analysis_direction == "pattern_analysis":
            prompt_parts.append("请分析八字格局模式，包括：")
            prompt_parts.append("1. 格局类型判断")
            prompt_parts.append("2. 用神喜忌分析")
            prompt_parts.append("3. 运势走向预测")
        elif analysis_direction == "comprehensive_pattern":
            prompt_parts.append("请进行综合格局分析，包括：")
            prompt_parts.append("1. 整体格局评估和层次判断")
            prompt_parts.append("2. 五行配置和强弱分析")
            prompt_parts.append("3. 十神组合和性格特征")
            prompt_parts.append("4. 神煞影响和特殊标志")
            prompt_parts.append("5. 大运流年和人生阶段")
            prompt_parts.append("6. 综合建议和发展方向")
        elif analysis_direction == "decision_support":
            prompt_parts.append("请针对具体问题提供决策建议：")
            if user_question:
                prompt_parts.append(f"用户问题：{user_question}")
            prompt_parts.append("1. 问题分析")
            prompt_parts.append("2. 八字角度的建议")
            prompt_parts.append("3. 最佳时机选择")
        
        prompt_parts.append("")
        
        # 根据领域添加专业要求
        domain_requirements = {
            "health": "重点关注健康养生、体质调理、疾病预防",
            "wealth": "重点关注财运分析、投资理财、财富积累",
            "education": "重点关注学业运势、考试时机、专业选择",
            "career": "重点关注事业发展、职业规划、升迁机会",
            "marriage": "重点关注婚恋情感、配偶特征、感情发展",
            "fortune": "重点关注吉运把握、机遇识别、顺势而为",
            "misfortune": "重点关注凶险规避、化解方法、趋吉避凶"
        }
        
        if domain in domain_requirements:
            prompt_parts.append(f"【分析重点】{domain_requirements[domain]}")
            prompt_parts.append("")
        
        # 根据时间范围添加时间要求
        time_requirements = {
            "yearly": "请重点分析年度运势变化",
            "monthly": "请重点分析月度运势起伏",
            "yearly_monthly": "请结合年运和月运进行综合分析",
            "daily": "请重点分析近期日常运势",
            "short_term": "请重点分析近期（3-6个月）运势",
            "long_term": "请重点分析长期（1-3年）运势趋势"
        }
        
        if time_scope in time_requirements:
            prompt_parts.append(f"【时间要求】{time_requirements[time_scope]}")
        
        return "\n".join(prompt_parts)
    
    def _get_base_system_prompt(self) -> str:
        """获取基础综合系统提示词"""
        return """你是专业的八字命理分析师，擅长根据八字信息提供精准的命理分析。请严格遵守以下规则：

【身份定位】
- 精通五行理论、十神分析、神煞解读等传统命理学
【结合格局】
- 分别结合最可能的吉祥格局和最可能的凶险格局进行分析，并量化列出格局的程度和概率，谈谈体现在性格的什么变化上
- 结合大运流年，分析大运流年带来的变数，包括风险和机遇

"""
    
    def _load_analysis_templates(self) -> Dict[str, str]:
        """加载分析方向模板"""
        return {
            "basic_analysis": "进行全面的基础分析，从五行、十神、格局等多个维度解读八字特征，为用户提供全面的命理认知。",
            "contradiction_analysis": "重点识别和分析八字中的冲突矛盾，包括五行相克、十神冲突等，并提供化解和调和的方法。",
            "pattern_analysis": "深入分析八字格局模式，判断格局高低，分析用神喜忌，预测运势发展趋势。",
            "comprehensive_pattern": "进行最全面的综合格局分析，从格局层次、五行配置、十神组合、神煞影响、运势走向等多个维度进行深度解读，提供完整的命理画像和人生指导。",
            "decision_support": "针对用户的具体问题和困惑，结合八字特征提供决策建议和最佳时机选择。"
        }
    
    def _load_domain_templates(self) -> Dict[str, str]:
        """加载领域模板"""
        return {
            "health": "结合中医理论和五行养生，分析体质特征，提供健康调理建议。",
            "wealth": "分析财运特征和财富积累能力，提供理财投资的时机建议。",
            "education": "分析学习天赋和考试运势，提供学业规划和考试时机建议。",
            "career": "分析事业发展潜力和职场特征，提供职业规划和发展建议。",
            "marriage": "分析感情特征和婚恋运势，提供情感发展和配偶选择建议。",
            "fortune": "分析吉运特征和机遇把握能力，提供趋吉避凶的建议。",
            "misfortune": "分析潜在风险和化解方法，提供规避凶险的策略建议。",
            "overall_pattern": "从整体格局角度进行全方位分析，涵盖命理结构、性格特征、人生走向等各个层面，提供完整的命理解读。"
        }
    
    def _load_time_templates(self) -> Dict[str, str]:
        """加载时间范围模板"""
        return {
            "yearly": "重点分析年度大运走势，结合流年干支进行运势预测。",
            "monthly": "重点分析月度运势变化，结合月令特征进行精准分析。",
            "yearly_monthly": "综合分析年运和月运的相互影响，提供全面的时间运势指导。",
            "daily": "重点分析近期日常运势，提供短期内的行动建议。",
            "short_term": "分析近期运势发展，提供3-6个月内的规划建议。",
            "long_term": "分析长期运势趋势，提供1-3年的发展规划建议。",
            "lifetime": "从人生全局角度进行分析，涵盖各个人生阶段的发展特征，提供终生的命理指导和人生规划建议。"
        }
    
    def _get_default_prompts(self) -> Tuple[str, str]:
        """获取默认提示词"""
        system_prompt = self.base_system_prompt
        user_prompt = """【八字分析请求】
八字：{eight_char}
性别：{gender}
出生日期：{birth_date}

请进行基础八字分析，提供个性化的命理指导建议。"""
        
        return system_prompt, user_prompt


# 全局实例
prompt_engine = BaziPromptEngine()
